<?php

namespace Tests\Feature\Api;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_admin_middleware_allows_admin_access()
    {
        $admin = User::where('role', 'admin')->first();

        $response = $this->actingAs($admin)
            ->getJson('/api/v1/admin/services');

        $response->assertStatus(200);
    }

    public function test_admin_middleware_blocks_non_admin()
    {
        $client = User::where('role', 'client')->first();

        $response = $this->actingAs($client)
            ->getJson('/api/v1/admin/services');

        $response->assertStatus(403);
    }

    public function test_admin_middleware_blocks_team_member()
    {
        $teamMember = User::where('role', 'team_member')->first();

        $response = $this->actingAs($teamMember)
            ->getJson('/api/v1/admin/services');

        $response->assertStatus(403);
    }

    public function test_admin_middleware_blocks_unauthenticated()
    {
        $response = $this->getJson('/api/v1/admin/services');

        $response->assertStatus(401);
    }

    public function test_role_middleware_allows_multiple_roles()
    {
        // Test with admin
        $admin = User::where('role', 'admin')->first();
        $response = $this->actingAs($admin)
            ->getJson('/api/v1/admin/team');
        $response->assertStatus(200);

        // Test with team member (if they had access to team endpoints)
        $teamMember = User::where('role', 'team_member')->first();
        $response = $this->actingAs($teamMember)
            ->getJson('/api/v1/admin/team');
        $response->assertStatus(403); // Should be blocked for team endpoints
    }

    public function test_authenticated_routes_require_authentication()
    {
        $response = $this->getJson('/api/v1/consultations');

        $response->assertStatus(401);
    }

    public function test_authenticated_routes_allow_authenticated_users()
    {
        $user = User::where('role', 'client')->first();

        $response = $this->actingAs($user)
            ->getJson('/api/v1/consultations');

        $response->assertStatus(200);
    }

    public function test_public_routes_accessible_without_authentication()
    {
        $response = $this->getJson('/api/v1/services');

        $response->assertStatus(200);
    }

    public function test_middleware_returns_json_for_api_requests()
    {
        $response = $this->getJson('/api/v1/admin/services');

        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => 'Authentication required',
            ]);
    }

    public function test_role_middleware_with_invalid_role()
    {
        // Create a user with an invalid role (this shouldn't happen in practice)
        $user = User::factory()->create(['role' => 'invalid_role']);

        $response = $this->actingAs($user)
            ->getJson('/api/v1/admin/services');

        $response->assertStatus(403);
    }

    public function test_middleware_preserves_request_data()
    {
        $admin = User::where('role', 'admin')->first();

        $serviceData = [
            'title' => 'Test Service',
            'description' => 'Test description',
            'category' => 'Test Category',
        ];

        $response = $this->actingAs($admin)
            ->postJson('/api/v1/admin/services', $serviceData);

        $response->assertStatus(201);
        $this->assertDatabaseHas('services', ['title' => 'Test Service']);
    }
}
