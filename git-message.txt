feat: Implement Phase 2 - Core Backend Infrastructure

- Add comprehensive API infrastructure with routes, controllers, and services
- Create base API controller with common functionality (pagination, search, error handling)
- Implement role-based access control middleware (AdminOnly, EnsureUserHasRole)
- Add service classes for business logic separation:
  * ServiceService - Service management with slug generation
  * BlogService - Blog post and category management
  * TeamService - Team member management with sort ordering
  * ContactService - Contact forms, newsletter, and user profiles
  * ConsultationService - Booking system with time slot validation
  * PaymentService - Payment processing with webhook handling
  * AnalyticsService - Event tracking and dashboard analytics

- Create API controllers for all major entities:
  * Public APIs: Services, Blog, Team, Contact, Analytics tracking
  * Protected APIs: User consultations, payments, profile management
  * Admin APIs: Full CRUD operations for all entities

- Add comprehensive test coverage:
  * Feature tests for API endpoints
  * Unit tests for service classes
  * Middleware authentication and authorization tests
  * Validation and error handling tests

- Implement proper API response formatting with consistent structure
- Add search, filtering, and pagination capabilities
- Create webhook endpoints for Paddle and CoinBase payment processing
- Establish logging and activity tracking throughout the system

Phase 2 complete: All backend infrastructure ready for frontend integration
