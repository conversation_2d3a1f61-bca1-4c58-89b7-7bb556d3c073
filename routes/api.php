<?php

use App\Http\Controllers\Api\AnalyticsController;
use App\Http\Controllers\Api\BlogController;
use App\Http\Controllers\Api\ContactController;
use App\Http\Controllers\Api\ConsultationController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\ServiceController;
use App\Http\Controllers\Api\TeamController;
use App\Http\Controllers\Api\Admin\AdminAnalyticsController;
use App\Http\Controllers\Api\Admin\AdminBlogController;
use App\Http\Controllers\Api\Admin\AdminConsultationController;
use App\Http\Controllers\Api\Admin\AdminContactController;
use App\Http\Controllers\Api\Admin\AdminPaymentController;
use App\Http\Controllers\Api\Admin\AdminServiceController;
use App\Http\Controllers\Api\Admin\AdminTeamController;
use App\Http\Controllers\Api\Admin\AdminUserController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public API Routes (no authentication required)
Route::prefix('v1')->group(function () {
    // Services
    Route::get('services', [ServiceController::class, 'index']);
    Route::get('services/{slug}', [ServiceController::class, 'show']);
    
    // Blog
    Route::get('blog/posts', [BlogController::class, 'index']);
    Route::get('blog/posts/{slug}', [BlogController::class, 'show']);
    Route::get('blog/categories', [BlogController::class, 'categories']);
    
    // Team
    Route::get('team', [TeamController::class, 'index']);
    
    // Contact & Newsletter
    Route::post('contact', [ContactController::class, 'store']);
    Route::post('newsletter', [ContactController::class, 'newsletter']);
    
    // Analytics (client-side tracking)
    Route::post('analytics/track', [AnalyticsController::class, 'track']);
});

// Protected API Routes (authentication required)
Route::prefix('v1')->middleware(['auth'])->group(function () {
    // User Profile
    Route::get('user/profile', [ContactController::class, 'profile']);
    Route::put('user/profile', [ContactController::class, 'updateProfile']);
    
    // Consultations
    Route::get('consultations', [ConsultationController::class, 'index']);
    Route::post('consultations', [ConsultationController::class, 'store']);
    Route::get('consultations/{consultation}', [ConsultationController::class, 'show']);
    Route::put('consultations/{consultation}', [ConsultationController::class, 'update']);
    
    // Payments
    Route::get('payments', [PaymentController::class, 'index']);
    Route::get('payments/{payment}', [PaymentController::class, 'show']);
});

// Admin API Routes (admin role required)
Route::prefix('v1/admin')->middleware(['auth', 'role:admin'])->group(function () {
    // Services Management
    Route::apiResource('services', AdminServiceController::class);
    
    // Blog Management
    Route::apiResource('blog/posts', AdminBlogController::class);
    Route::apiResource('blog/categories', AdminBlogController::class, ['only' => ['index', 'store', 'show', 'update', 'destroy']]);
    
    // Team Management
    Route::apiResource('team', AdminTeamController::class);
    
    // User Management
    Route::apiResource('users', AdminUserController::class);
    
    // Consultation Management
    Route::get('consultations', [AdminConsultationController::class, 'index']);
    Route::get('consultations/{consultation}', [AdminConsultationController::class, 'show']);
    Route::put('consultations/{consultation}', [AdminConsultationController::class, 'update']);
    Route::delete('consultations/{consultation}', [AdminConsultationController::class, 'destroy']);
    
    // Payment Management
    Route::get('payments', [AdminPaymentController::class, 'index']);
    Route::get('payments/{payment}', [AdminPaymentController::class, 'show']);
    Route::put('payments/{payment}', [AdminPaymentController::class, 'update']);
    
    // Contact & Newsletter Management
    Route::get('contact-submissions', [AdminContactController::class, 'contactSubmissions']);
    Route::get('newsletter-subscriptions', [AdminContactController::class, 'newsletterSubscriptions']);
    Route::put('contact-submissions/{submission}', [AdminContactController::class, 'updateContactSubmission']);
    Route::delete('contact-submissions/{submission}', [AdminContactController::class, 'deleteContactSubmission']);
    
    // Analytics Dashboard
    Route::get('analytics/dashboard', [AdminAnalyticsController::class, 'dashboard']);
    Route::get('analytics/events', [AdminAnalyticsController::class, 'events']);
    Route::get('analytics/performance', [AdminAnalyticsController::class, 'performance']);
    Route::put('analytics/settings', [AdminAnalyticsController::class, 'updateSettings']);
});

// Webhook Routes (no authentication, but verified by signature)
Route::prefix('webhooks')->group(function () {
    Route::post('paddle', [PaymentController::class, 'paddleWebhook']);
    Route::post('coinbase', [PaymentController::class, 'coinbaseWebhook']);
});
