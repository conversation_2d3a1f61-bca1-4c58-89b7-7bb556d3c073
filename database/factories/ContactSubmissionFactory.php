<?php

namespace Database\Factories;

use App\Models\ContactSubmission;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ContactSubmissionFactory extends Factory
{
    protected $model = ContactSubmission::class;

    public function definition(): array
    {
        return [
            'user_id' => null, // Can be null for anonymous submissions
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'company' => $this->faker->company(),
            'subject' => $this->faker->sentence(),
            'message' => $this->faker->paragraph(3),
            'status' => $this->faker->randomElement(['pending', 'read', 'replied']),
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
        ];
    }

    public function withUser(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'user_id' => User::factory(),
            ];
        });
    }

    public function pending(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
            ];
        });
    }

    public function read(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'read',
            ];
        });
    }
}
