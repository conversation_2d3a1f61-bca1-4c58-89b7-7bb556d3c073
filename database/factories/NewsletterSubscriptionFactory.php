<?php

namespace Database\Factories;

use App\Models\NewsletterSubscription;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class NewsletterSubscriptionFactory extends Factory
{
    protected $model = NewsletterSubscription::class;

    public function definition(): array
    {
        return [
            'user_id' => null, // Can be null for anonymous subscriptions
            'email' => $this->faker->unique()->safeEmail(),
            'preferences' => $this->faker->randomElements(['marketing', 'updates', 'newsletters'], 2),
            'is_active' => true,
            'subscribed_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'unsubscribed_at' => null,
        ];
    }

    public function withUser(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'user_id' => User::factory(),
            ];
        });
    }

    public function inactive(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
                'unsubscribed_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
            ];
        });
    }

    public function active(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
                'unsubscribed_at' => null,
            ];
        });
    }
}
