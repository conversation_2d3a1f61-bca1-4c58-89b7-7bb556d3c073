<?php

namespace App\Services;

use App\Models\ContactSubmission;
use App\Models\NewsletterSubscription;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;

class ContactService extends BaseService
{
    public function __construct()
    {
        $this->model = new ContactSubmission();
    }

    /**
     * Submit contact form
     */
    public function submitContactForm(array $data): ContactSubmission
    {
        $validated = $this->validateContactData($data);
        
        // Add IP address and user agent
        $validated['ip_address'] = request()->ip();
        $validated['user_agent'] = request()->userAgent();
        
        // Set user ID if authenticated
        if (auth()->check()) {
            $validated['user_id'] = auth()->id();
        }

        $submission = ContactSubmission::create($validated);
        
        $this->logActivity('contact_submitted', $submission);
        
        // TODO: Send notification email to admin
        // TODO: Send confirmation email to user
        
        return $submission;
    }

    /**
     * Subscribe to newsletter
     */
    public function subscribeToNewsletter(array $data): NewsletterSubscription
    {
        $validated = $this->validateNewsletterData($data);
        
        // Check if already subscribed
        $existing = NewsletterSubscription::where('email', $validated['email'])->first();
        
        if ($existing) {
            if ($existing->is_active) {
                throw new \Exception('Email is already subscribed to newsletter');
            } else {
                // Reactivate subscription
                $existing->update([
                    'is_active' => true,
                    'subscribed_at' => now(),
                    'preferences' => $validated['preferences'] ?? [],
                ]);
                return $existing;
            }
        }

        // Create new subscription
        $validated['subscribed_at'] = now();
        $validated['is_active'] = true;
        
        // Set user ID if authenticated
        if (auth()->check()) {
            $validated['user_id'] = auth()->id();
        }

        $subscription = NewsletterSubscription::create($validated);
        
        $this->logActivity('newsletter_subscribed', $subscription);
        
        // TODO: Send welcome email
        
        return $subscription;
    }

    /**
     * Unsubscribe from newsletter
     */
    public function unsubscribeFromNewsletter(string $email): bool
    {
        $subscription = NewsletterSubscription::where('email', $email)->first();
        
        if (!$subscription) {
            throw new \Exception('Email not found in newsletter subscriptions');
        }

        $subscription->update([
            'is_active' => false,
            'unsubscribed_at' => now(),
        ]);

        $this->logActivity('newsletter_unsubscribed', $subscription);
        
        return true;
    }

    /**
     * Get user profile
     */
    public function getUserProfile(int $userId): ?User
    {
        return User::find($userId);
    }

    /**
     * Update user profile
     */
    public function updateUserProfile(int $userId, array $data): User
    {
        $user = User::findOrFail($userId);
        
        $validated = $this->validateProfileData($data);
        
        // Handle password update separately
        if (isset($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        }

        $user->update($validated);
        
        $this->logActivity('profile_updated', $user);
        
        return $user->refresh();
    }

    /**
     * Get contact submissions with filters
     */
    public function getContactSubmissions(array $filters = [])
    {
        $query = ContactSubmission::with('user');

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        // Apply search
        if (isset($filters['search']) && !empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('email', 'LIKE', "%{$search}%")
                  ->orWhere('subject', 'LIKE', "%{$search}%")
                  ->orWhere('message', 'LIKE', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        $paginate = $filters['paginate'] ?? true;
        if ($paginate) {
            $limit = min($filters['limit'] ?? $this->defaultLimit, $this->maxLimit);
            return $query->paginate($limit);
        }

        return $query->get();
    }

    /**
     * Get newsletter subscriptions with filters
     */
    public function getNewsletterSubscriptions(array $filters = [])
    {
        $query = NewsletterSubscription::with('user');

        // Apply filters
        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('subscribed_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('subscribed_at', '<=', $filters['date_to']);
        }

        // Apply search
        if (isset($filters['search']) && !empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('email', 'LIKE', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'LIKE', "%{$search}%");
                  });
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'subscribed_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        $paginate = $filters['paginate'] ?? true;
        if ($paginate) {
            $limit = min($filters['limit'] ?? $this->defaultLimit, $this->maxLimit);
            return $query->paginate($limit);
        }

        return $query->get();
    }

    /**
     * Update contact submission status
     */
    public function updateContactSubmissionStatus(int $id, string $status): ContactSubmission
    {
        $submission = ContactSubmission::findOrFail($id);
        $submission->update(['status' => $status]);
        
        $this->logActivity('contact_status_updated', $submission);
        
        return $submission;
    }

    /**
     * Validate contact form data
     */
    private function validateContactData(array $data): array
    {
        return validator($data, [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ])->validate();
    }

    /**
     * Validate newsletter subscription data
     */
    private function validateNewsletterData(array $data): array
    {
        return validator($data, [
            'email' => 'required|email|max:255',
            'preferences' => 'nullable|array',
        ])->validate();
    }

    /**
     * Validate profile update data
     */
    private function validateProfileData(array $data): array
    {
        $rules = [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|max:255|unique:users,email,' . auth()->id(),
            'phone' => 'sometimes|nullable|string|max:20',
            'company' => 'sometimes|nullable|string|max:255',
            'avatar' => 'sometimes|nullable|string|max:255',
        ];

        // Add password validation if provided
        if (isset($data['password'])) {
            $rules['password'] = 'required|string|min:8|confirmed';
        }

        return validator($data, $rules)->validate();
    }

    /**
     * Get validation rules for create (not used for this service)
     */
    protected function getCreateRules(): array
    {
        return [];
    }

    /**
     * Get validation rules for update (not used for this service)
     */
    protected function getUpdateRules(Model $record): array
    {
        return [];
    }
}
