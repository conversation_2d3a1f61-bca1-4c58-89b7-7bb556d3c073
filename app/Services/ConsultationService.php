<?php

namespace App\Services;

use App\Models\Consultation;
use App\Models\Service;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ConsultationService extends BaseService
{
    public function __construct()
    {
        $this->model = new Consultation();
    }

    /**
     * Book a consultation
     */
    public function bookConsultation(array $data): Consultation
    {
        $validated = $this->validateBookingData($data);
        
        // Set user ID if authenticated
        if (auth()->check()) {
            $validated['user_id'] = auth()->id();
        }

        // Validate service exists and is active
        $service = Service::where('id', $validated['service_id'])
            ->where('is_active', true)
            ->first();
            
        if (!$service) {
            throw new \Exception('Service not found or not available');
        }

        // Check if the consultation date/time is available
        if (!$this->isTimeSlotAvailable($validated['consultation_date'], $validated['duration'] ?? 60)) {
            throw new \Exception('The selected time slot is not available');
        }

        // Set default values
        $validated['status'] = 'pending';
        $validated['payment_status'] = 'pending';

        $consultation = Consultation::create($validated);
        
        $this->logActivity('consultation_booked', $consultation);
        
        // TODO: Send confirmation email
        // TODO: Create calendar event
        // TODO: Send notification to admin
        
        return $consultation;
    }

    /**
     * Get user's consultations
     */
    public function getUserConsultations(int $userId, array $filters = [])
    {
        $query = $this->model->with(['service', 'user'])
            ->where('user_id', $userId);

        return $this->applyFiltersAndPaginate($query, $filters);
    }

    /**
     * Get all consultations (admin)
     */
    public function getAllConsultations(array $filters = [])
    {
        $query = $this->model->with(['service', 'user']);

        return $this->applyFiltersAndPaginate($query, $filters);
    }

    /**
     * Update consultation status
     */
    public function updateStatus(int $id, string $status): Consultation
    {
        $consultation = $this->findByIdOrFail($id);
        
        $validStatuses = ['pending', 'confirmed', 'completed', 'cancelled'];
        if (!in_array($status, $validStatuses)) {
            throw new \Exception('Invalid status');
        }

        $consultation->update(['status' => $status]);
        
        $this->logActivity('consultation_status_updated', $consultation);
        
        // TODO: Send status update email
        
        return $consultation->refresh();
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus(int $id, string $paymentStatus, string $paymentId = null): Consultation
    {
        $consultation = $this->findByIdOrFail($id);
        
        $validStatuses = ['pending', 'completed', 'failed', 'refunded'];
        if (!in_array($paymentStatus, $validStatuses)) {
            throw new \Exception('Invalid payment status');
        }

        $updateData = ['payment_status' => $paymentStatus];
        if ($paymentId) {
            $updateData['payment_id'] = $paymentId;
        }

        $consultation->update($updateData);
        
        // Auto-confirm consultation if payment is completed
        if ($paymentStatus === 'completed' && $consultation->status === 'pending') {
            $consultation->update(['status' => 'confirmed']);
        }
        
        $this->logActivity('consultation_payment_updated', $consultation);
        
        return $consultation->refresh();
    }

    /**
     * Check if time slot is available
     */
    public function isTimeSlotAvailable(string $dateTime, int $duration = 60): bool
    {
        $startTime = Carbon::parse($dateTime);
        $endTime = $startTime->copy()->addMinutes($duration);

        // Check for overlapping consultations
        $overlapping = $this->model
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($startTime, $endTime) {
                $query->whereBetween('consultation_date', [$startTime, $endTime])
                    ->orWhere(function ($q) use ($startTime, $endTime) {
                        $q->where('consultation_date', '<', $startTime)
                          ->whereRaw('DATE_ADD(consultation_date, INTERVAL duration MINUTE) > ?', [$startTime]);
                    });
            })
            ->exists();

        return !$overlapping;
    }

    /**
     * Get available time slots for a date
     */
    public function getAvailableTimeSlots(string $date, int $duration = 60): array
    {
        $date = Carbon::parse($date)->startOfDay();
        $workingHours = [
            'start' => 9, // 9 AM
            'end' => 17,  // 5 PM
        ];

        $slots = [];
        $current = $date->copy()->addHours($workingHours['start']);
        $endOfDay = $date->copy()->addHours($workingHours['end']);

        while ($current->addMinutes($duration)->lte($endOfDay)) {
            if ($this->isTimeSlotAvailable($current->toDateTimeString(), $duration)) {
                $slots[] = [
                    'datetime' => $current->toDateTimeString(),
                    'formatted' => $current->format('H:i'),
                ];
            }
            $current->addMinutes(30); // 30-minute intervals
        }

        return $slots;
    }

    /**
     * Apply filters and pagination
     */
    private function applyFiltersAndPaginate($query, array $filters)
    {
        // Apply filters
        $query = $this->applyFilters($query, $filters);

        // Apply search
        if (isset($filters['search']) && !empty($filters['search'])) {
            $query = $this->applySearch($query, $filters['search']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'consultation_date';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query = $this->applySorting($query, $sortBy, $sortDirection);

        $paginate = $filters['paginate'] ?? true;
        if ($paginate) {
            $limit = min($filters['limit'] ?? $this->defaultLimit, $this->maxLimit);
            return $query->paginate($limit);
        }

        return $query->get();
    }

    /**
     * Apply filters to query
     */
    protected function applyFilters($query, array $filters)
    {
        // Filter by status
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Filter by payment status
        if (isset($filters['payment_status'])) {
            $query->where('payment_status', $filters['payment_status']);
        }

        // Filter by service
        if (isset($filters['service_id']) && !empty($filters['service_id'])) {
            $query->where('service_id', $filters['service_id']);
        }

        // Filter by date range
        if (isset($filters['date_from'])) {
            $query->whereDate('consultation_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('consultation_date', '<=', $filters['date_to']);
        }

        return $query;
    }

    /**
     * Apply search to query
     */
    protected function applySearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('notes', 'LIKE', "%{$search}%")
              ->orWhereHas('user', function ($userQuery) use ($search) {
                  $userQuery->where('name', 'LIKE', "%{$search}%")
                           ->orWhere('email', 'LIKE', "%{$search}%");
              })
              ->orWhereHas('service', function ($serviceQuery) use ($search) {
                  $serviceQuery->where('title', 'LIKE', "%{$search}%");
              });
        });
    }

    /**
     * Get allowed sort fields
     */
    protected function getAllowedSortFields(): array
    {
        return [
            'id', 'consultation_date', 'duration', 'status', 'payment_status',
            'created_at', 'updated_at'
        ];
    }

    /**
     * Validate booking data
     */
    private function validateBookingData(array $data): array
    {
        return validator($data, [
            'service_id' => 'required|exists:services,id',
            'consultation_date' => 'required|date|after:now',
            'duration' => 'nullable|integer|min:30|max:240', // 30 minutes to 4 hours
            'notes' => 'nullable|string|max:1000',
        ])->validate();
    }

    /**
     * Get validation rules for create
     */
    protected function getCreateRules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
            'service_id' => 'required|exists:services,id',
            'consultation_date' => 'required|date',
            'duration' => 'nullable|integer|min:30|max:240',
            'status' => 'required|in:pending,confirmed,completed,cancelled',
            'notes' => 'nullable|string|max:1000',
            'meeting_link' => 'nullable|string|max:255',
            'payment_status' => 'required|in:pending,completed,failed,refunded',
            'payment_id' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get validation rules for update
     */
    protected function getUpdateRules(Model $record): array
    {
        return [
            'consultation_date' => 'sometimes|required|date',
            'duration' => 'sometimes|nullable|integer|min:30|max:240',
            'status' => 'sometimes|required|in:pending,confirmed,completed,cancelled',
            'notes' => 'sometimes|nullable|string|max:1000',
            'meeting_link' => 'sometimes|nullable|string|max:255',
            'payment_status' => 'sometimes|required|in:pending,completed,failed,refunded',
            'payment_id' => 'sometimes|nullable|string|max:255',
        ];
    }
}
